"use client";

import { Lead } from "@/types/lead";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import type { ColumnDef } from "@tanstack/react-table";
import * as React from "react";

import { DataTableColumnHeader } from "@flinkk/data-table/component/data-table-column-header";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import { Badge } from "@flinkk/components/ui/badge";
import { Avatar, AvatarFallback } from "@flinkk/components/ui/avatar";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@flinkk/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";
import { getDisplayName } from "@/utils/name-utils";
import Link from "next/link";
import {
  BoringAvatar,
  avatarPresets,
} from "@flinkk/components/ui/boring-avatar";

// Helper function to format date
function formatDate(dateString: string): string {
  if (!dateString) return "—";

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "Invalid date";

  // Format the date as "MMM DD, YYYY"
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface GetLeadsTableColumnsProps {
  setRowAction?: (action: DataTableRowAction<Lead> | null) => void;
  permissions?: ModelPermissions;
}

// Helper function to format status
function formatStatus(status: string | null | undefined): string {
  if (!status) return "Unknown";

  return status
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

// Helper function to get status color
function getStatusColor(status: string | null | undefined): string {
  if (!status) return "bg-gray-50 text-gray-700 hover:bg-gray-100";

  const statusMap: Record<string, string> = {
    NEW: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    CONTACTED: "bg-indigo-50 text-indigo-700 hover:bg-indigo-100",
    QUALIFIED: "bg-green-50 text-green-700 hover:bg-green-100",
    UNQUALIFIED: "bg-red-50 text-red-700 hover:bg-red-100",
    NURTURING: "bg-yellow-50 text-yellow-700 hover:bg-yellow-100",
    CONVERTED: "bg-purple-50 text-purple-700 hover:bg-purple-100",
    LOST: "bg-gray-50 text-gray-700 hover:bg-gray-100",
  };

  return statusMap[status] || "bg-gray-50 text-gray-700 hover:bg-gray-100";
}

export function getLeadsTableColumns({
  setRowAction,
  permissions = {
    canView: true,
    canCreate: false,
    canEdit: false,
    canDelete: false,
  },
}: GetLeadsTableColumnsProps): ColumnDef<Lead>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-0.5 rounded"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-0.5 rounded"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40,
    },
    {
      id: "firstName",
      accessorFn: (row) =>
        getDisplayName(row.firstName, row.lastName, null, row.email),
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Lead" />
      ),
      cell: ({ row }) => {
        const lead = row.original;
        const displayName = getDisplayName(
          lead.firstName,
          lead.lastName,
          null,
          lead.email,
        );

        // Generate seed for consistent avatar using email or name
        const avatarSeed = lead.email || displayName || `lead-${lead.id}`;

        return (
          <div className="flex items-center gap-3">
            {/* Lead Avatar (Left) */}
            <BoringAvatar
              name={avatarSeed}
              size="default"
              {...avatarPresets.lead}
            />

            {/* Lead Info (Right) */}
            <div className="flex flex-col min-w-0 flex-1">
              <div className="font-medium text-gray-900 truncate">
                {displayName || "Unknown Lead"}
              </div>
              <div className="text-sm text-gray-500 truncate">
                {lead.email || "No email"}
              </div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
      meta: {
        label: "Lead",
        variant: "text",
        placeholder: "Search by name, email, or company...",
      },
      enableColumnFilter: true,
      size: 220, // Set minimum width for the column
    },

    {
      id: "phone",
      accessorKey: "phone",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Phone" />
      ),
      cell: ({ row }) => <div>{row.getValue("phone") || "—"}</div>,
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      id: "company",
      accessorKey: "company",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Company" />
      ),
      cell: ({ row }) => <div>{row.getValue("company") || "—"}</div>,
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      id: "requirements",
      accessorKey: "requirements",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Requirements" />
      ),
      cell: ({ row }) => {
        const requirements = row.getValue("requirements") as string;
        if (!requirements) return <div className="text-gray-500">—</div>;

        // Truncate long requirements text
        const truncated =
          requirements.length > 50
            ? requirements.substring(0, 50) + "..."
            : requirements;

        return (
          <div className="max-w-[200px] truncate" title={requirements}>
            {truncated}
          </div>
        );
      },
      enableSorting: false,
      enableColumnFilter: false,
      size: 200,
    },
    {
      id: "stage",
      accessorKey: "stage",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const stage = row.getValue("stage") as string;
        return (
          <Badge variant="outline" className={getStatusColor(stage)}>
            {formatStatus(stage)}
          </Badge>
        );
      },
      enableSorting: true,
      meta: {
        label: "Status",
        variant: "select",
        options: [
          { label: "New", value: "NEW" },
          { label: "Contacted", value: "CONTACTED" },
          { label: "Qualified", value: "QUALIFIED" },
          { label: "Unqualified", value: "UNQUALIFIED" },
          { label: "Nurturing", value: "NURTURING" },
          { label: "Converted", value: "CONVERTED" },
          { label: "Lost", value: "LOST" },
        ],
      },
      enableColumnFilter: true,
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as string;
        return <div>{formatDate(date)}</div>;
      },
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const lead = row.original;

        // Don't show actions column if user has no edit or delete permissions
        if (!permissions.canEdit && !permissions.canDelete) {
          return null;
        }

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {permissions.canEdit && (
                <DropdownMenuItem asChild>
                  <Link
                    href={`/leads/${lead.id}?title=Edit - ${lead.firstName}`}
                  >
                    Edit
                  </Link>
                </DropdownMenuItem>
              )}
              {permissions.canDelete && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setRowAction?.({
                      variant: "delete",
                      row: row,
                    });
                  }}
                >
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
